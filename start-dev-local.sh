#!/bin/bash

# 开发环境启动脚本 - 使用本地MySQL
# 不需要Docker，直接使用本地安装的MySQL

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}    RTPosServer 开发环境启动脚本${NC}"
echo -e "${BLUE}    使用本地MySQL数据库${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 检查MySQL是否运行
check_mysql() {
    echo -e "${YELLOW}🔍 检查本地MySQL服务...${NC}"
    
    # 尝试连接MySQL
    if command -v mysql &> /dev/null; then
        echo -e "${GREEN}✅ MySQL客户端已安装${NC}"
        
        # 检查MySQL服务是否运行
        if mysqladmin ping -h localhost --silent 2>/dev/null; then
            echo -e "${GREEN}✅ MySQL服务正在运行${NC}"
            mysql --version
        else
            echo -e "${RED}❌ MySQL服务未运行${NC}"
            echo -e "${YELLOW}请启动MySQL服务：${NC}"
            echo -e "  macOS: brew services start mysql"
            echo -e "  或者: sudo /usr/local/mysql/support-files/mysql.server start"
            echo -e "  Linux: sudo systemctl start mysql"
            echo -e "  Windows: net start mysql"
            exit 1
        fi
    else
        echo -e "${RED}❌ MySQL客户端未安装${NC}"
        echo -e "${YELLOW}请安装MySQL：${NC}"
        echo -e "  macOS: brew install mysql"
        echo -e "  Linux: sudo apt-get install mysql-server"
        exit 1
    fi
}

# 创建开发数据库
create_database() {
    echo -e "${YELLOW}🗄️ 设置开发数据库...${NC}"
    
    # 提示输入MySQL root密码
    echo -e "${BLUE}请输入MySQL root密码（如果没有密码直接按回车）：${NC}"
    read -s mysql_password
    
    # 设置环境变量
    export MYSQL_PASSWORD="$mysql_password"
    
    # 创建数据库和用户
    mysql -uroot -p"$mysql_password" << EOF
-- 创建开发数据库
CREATE DATABASE IF NOT EXISTS rtpos_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建开发用户（可选）
CREATE USER IF NOT EXISTS 'rtpos_dev'@'localhost' IDENTIFIED BY 'rtpos_dev123';

-- 授权
GRANT ALL PRIVILEGES ON rtpos_dev.* TO 'rtpos_dev'@'localhost';
GRANT ALL PRIVILEGES ON rtpos_dev.* TO 'root'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;

-- 显示数据库
SHOW DATABASES;
EOF

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 数据库设置完成${NC}"
    else
        echo -e "${RED}❌ 数据库设置失败，请检查MySQL密码${NC}"
        exit 1
    fi
}

# 检查Redis（可选）
check_redis() {
    echo -e "${YELLOW}🔍 检查Redis服务...${NC}"
    
    if command -v redis-cli &> /dev/null; then
        if redis-cli ping > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Redis服务正在运行${NC}"
        else
            echo -e "${YELLOW}⚠️  Redis服务未运行（可选服务）${NC}"
            echo -e "${BLUE}如需启动Redis：${NC}"
            echo -e "  macOS: brew services start redis"
            echo -e "  Linux: sudo systemctl start redis"
        fi
    else
        echo -e "${YELLOW}⚠️  Redis未安装（可选服务）${NC}"
        echo -e "${BLUE}如需安装Redis：${NC}"
        echo -e "  macOS: brew install redis"
        echo -e "  Linux: sudo apt-get install redis-server"
    fi
}

# 主要流程
main() {
    check_mysql
    echo ""
    
    create_database
    echo ""
    
    check_redis
    echo ""
    
    echo -e "${GREEN}🎉 环境检查完成！${NC}"
    echo ""
    echo -e "${BLUE}数据库连接信息:${NC}"
    echo -e "  📊 MySQL:"
    echo -e "    - 主机: localhost:3306"
    echo -e "    - 数据库: rtpos_dev"
    echo -e "    - 用户名: root"
    echo -e "    - 密码: [您刚才输入的密码]"
    echo ""
    
    # 检查Maven包装器
    if [ ! -f "./mvnw" ]; then
        echo -e "${RED}❌ 找不到Maven包装器，请确保在项目根目录运行此脚本${NC}"
        exit 1
    fi
    
    echo -e "${YELLOW}🔨 编译项目...${NC}"
    ./mvnw clean compile
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 编译成功${NC}"
        echo ""
        echo -e "${YELLOW}🚀 启动应用...${NC}"
        echo ""
        echo -e "${BLUE}应用访问地址:${NC}"
        echo -e "  - API文档: http://localhost:8081/api/v1/swagger-ui.html"
        echo -e "  - 健康检查: http://localhost:8081/api/v1/actuator/health"
        echo -e "  - API基础路径: http://localhost:8081/api/v1"
        echo ""
        echo -e "${BLUE}默认认证信息:${NC}"
        echo -e "  - 用户名: admin"
        echo -e "  - 密码: admin123"
        echo ""
        echo -e "${YELLOW}💡 提示:${NC}"
        echo -e "  - 使用本地MySQL，无需Docker"
        echo -e "  - 数据会持久化保存在MySQL中"
        echo -e "  - 可以使用MySQL客户端工具管理数据库"
        echo -e "  - 按 Ctrl+C 停止应用"
        echo ""
        echo -e "${BLUE}========================================${NC}"
        
        # 启动应用
        ./mvnw spring-boot:run -Dspring-boot.run.profiles=dev
    else
        echo -e "${RED}❌ 编译失败，请检查错误信息${NC}"
        exit 1
    fi
}
  
# 执行主流程
main
